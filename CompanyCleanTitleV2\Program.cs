using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using LanguageDetection;

namespace CompanyCleanTitleV2
{
    internal class Program
    {
        static void Main(string[] args)
        {
            // Test cases for GetDeepCleanCompanyNameV1
            var testCases = new Dictionary<string, string>
            {
                { "Kellogg Brown & Root LLC", "Kellogg Brown & Root" },
                { "Siemens AG", "Siemens" },
                { "Telefónica S.A.", "Telefónica" },
                { "株式会社東芝", "東芝" },
                { "भारत लिमिटेड", "भारत" },
                { "Bayerische Motoren Werke AG", "Bayerische Motoren Werke" },
                { "Nestlé S.A.", "Nestlé" },
                { "Gazprom OAO", "Gazprom" },
                { "Companhia Vale do Rio Doce S.A.", "Companhia Vale do Rio Doce" },
                { "Volkswagen Aktiengesellschaft", "Volkswagen" },
                { "Samsung Electronics Co., Ltd.", "Samsung Electronic" },
                { "Tata Consultancy Services Limited", "Tata Consultancy Service" },
                { "中国石油天然气集团公司", "中国石油天然气集团公司" }, // Should remain unchanged if no suffix
                { "شركة أرامكو السعودية", "شركة أرامكو السعودية" }, // Arabic, no suffix
                { "한국전력공사", "한국전력공사" }, // Korean, no suffix
            };

            Console.WriteLine("Testing GetDeepCleanCompanyNameV1:");
            Console.WriteLine("=================================");

            foreach (var test in testCases)
            {
                var result = GetDeepCleanCompanyName(test.Key);
                var passed = result == test.Value;
                Console.WriteLine($"Input: {test.Key}");
                Console.WriteLine($"Expected: {test.Value}");
                Console.WriteLine($"Got: '{result}'");
                Console.WriteLine($"Test {(passed ? "PASSED" : "FAILED")}");
                Console.WriteLine("-------------------");
            }
        }

        // Dictionary to store language-specific patterns
        private static readonly Dictionary<string, LanguagePatterns> LanguageWordSets = InitializeLanguageWordSets();

        // Class to hold patterns for each language
        private class LanguagePatterns
        {
            public string SuffixPattern { get; set; }
            public string ConjunctionPattern { get; set; }
            public string PossessivePattern { get; set; }
        }

        // Initialize language-specific word sets
        private static Dictionary<string, LanguagePatterns> InitializeLanguageWordSets()
        {
            var wordSets = new Dictionary<string, LanguagePatterns>
        {
            // English
            {
                "en", new LanguagePatterns
                {
                    SuffixPattern = @"\b(incorporated|corporation|Corporate|Cooperatives|Cooperative|coop|inc|llc|corp|corps|company|limited|ltd|co|LLP|LP|L\.L\.C|Co\.,? Ltd\.|Co\.|Ltd\.|S\.A\.|S\.A\.?|OAO|Aktiengesellschaft)\b",
                    ConjunctionPattern = @"\b(and|of|in|by|for|or)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // German
            {
                "de", new LanguagePatterns
                {
                    SuffixPattern = @"\b(GmbH|AG|KG|OHG|EG|SE)\b",
                    ConjunctionPattern = @"\b(und|von|in|bei|für|oder)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // French
            {
                "fr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|SARL|SAS|SE)\b",
                    ConjunctionPattern = @"\b(et|de|à|pour|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Spanish
            {
                "es", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.?|SL|SRL)\b",
                    ConjunctionPattern = @"\b(y|de|en|por|para|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Italian
            {
                "it", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SpA|SRL|SRLS)\b",
                    ConjunctionPattern = @"\b(e|di|in|per|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Portuguese
            {
                "pt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|Lda)\b",
                    ConjunctionPattern = @"\b(e|de|em|por|para|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Dutch
            {
                "nl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(NV|BV|Vof)\b",
                    ConjunctionPattern = @"\b(en|van|in|voor|of)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Polish
            {
                "pl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sp\.z\.o\.o\.|SA|s\.c\.)\b",
                    ConjunctionPattern = @"\b(i|w|z|lub)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Swedish
            {
                "sv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(AB)\b",
                    ConjunctionPattern = @"\b(och|i|för|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Danish
            {
                "da", new LanguagePatterns
                {
                    SuffixPattern = @"\b(A\/S|ApS)\b",
                    ConjunctionPattern = @"\b(og|i|for|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Finnish
            {
                "fi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Oy|OYJ)\b",
                    ConjunctionPattern = @"\b(ja|in|tai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Czech
            {
                "cs", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.|a\.s\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|nebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovak
            {
                "sk", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|alebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Greek
            {
                "el", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ΑΕ|ΕΠΕ)\b",
                    ConjunctionPattern = @"\b(και|σε|για|ή)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Bulgarian
            {
                "bg", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ЕООД|АД)\b",
                    ConjunctionPattern = @"\b(и|в|за|или)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hungarian
            {
                "hu", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Kft|Zrt|Nyrt)\b",
                    ConjunctionPattern = @"\b(és|ban|ben|val|vel|vagy)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Croatian
            {
                "hr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(i|u|za|ili)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Estonian
            {
                "et", new LanguagePatterns
                {
                    SuffixPattern = @"\b(OÜ|AS)\b",
                    ConjunctionPattern = @"\b(ja|või)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Latvian
            {
                "lv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SIA|AS)\b",
                    ConjunctionPattern = @"\b(un|vai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Lithuanian
            {
                "lt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(UAB)\b",
                    ConjunctionPattern = @"\b(ir|ar)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Romanian
            {
                "ro", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SRL|SA)\b",
                    ConjunctionPattern = @"\b(şi|în|pentru|sau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovene
            {
                "sl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(in|za|ali)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Maltese
            {
                "mt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Ltd)\b",
                    ConjunctionPattern = @"\b(u|ta’|għal|jew)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hindi
            {
                "hi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(लिमिटेड|प्राइवेट|पब्लिक)\b",
                    ConjunctionPattern = @"\b(और|में|के|या)\b",
                    PossessivePattern = @"['’]s\b",
                }
            },
            // Malay
            {
                "ms", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sdn Bhd|Bhd)\b",
                    ConjunctionPattern = @"\b(dan|di|untuk|atau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Mandarin
            {
                "zh", new LanguagePatterns
                {
                    SuffixPattern = @"\b(有限公司|股份公司)\b",
                    ConjunctionPattern = @"\b(和|在|为|或)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Tamil
            {
                "ta", new LanguagePatterns
                {
                    SuffixPattern = @"\b(லிமிடெட்|பிரைவேட்)\b",
                    ConjunctionPattern = @"\b(மற்றும்|இல்|அல்லது)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Japanese
            {
                "ja", new LanguagePatterns
                {
                    // Match 株式会社 or 合同会社 at the start, or surrounded by spaces (handles both cases)
                    SuffixPattern = @"^(株式会社|合同会社)|\s(株式会社|合同会社)\s?",
                    ConjunctionPattern = @"(?:^|\s)(と|で|に|や|または)(?:\s|$)",
                    PossessivePattern = @"['’]s\b",
                }
            },

            // Add more languages as needed (e.g., Māori, Irish, Luxembourgish, etc.)
        };
            return wordSets;
        }

        public static string GetDeepCleanCompanyName(string companyName)
        {
            if (string.IsNullOrEmpty(companyName)) return string.Empty;

            var detector = new LanguageDetector();
            detector.AddAllLanguages();
            string detectedLanguage = detector.Detect(companyName) ?? "en";

            var cleanCompanyName = companyName;

            // Step 1: Remove parentheses content and special characters (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\(.*?\)", "");
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"[""']", "");

            // Step 2: Handle websites (language-independent)
            var matchWebSite = Regex.Match(cleanCompanyName, @"\p{L}+\.(com|co|org|net|edu|gov|mil|biz|info|eu|de|fr|uk|es|it|jp|cn|in|sg|au|nz)", RegexOptions.IgnoreCase);
            if (matchWebSite.Success)
                return matchWebSite.Value;

            // Step 3: Apply language-specific patterns
            if (LanguageWordSets.TryGetValue(detectedLanguage, out var langPatterns))
            {
                // Remove corporate suffixes for detected language first
                cleanCompanyName = Regex.Replace(cleanCompanyName, langPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);

                // Remove conjunctions and possessives for detected language
                var combinedPattern = $@"({langPatterns.ConjunctionPattern}|{langPatterns.PossessivePattern})";
                cleanCompanyName = Regex.Replace(cleanCompanyName, combinedPattern, " ", RegexOptions.IgnoreCase);
            }

            // Step 4: Apply common English patterns as fallback
            if (detectedLanguage != "en" && LanguageWordSets.TryGetValue("en", out var enPatterns))
            {
                cleanCompanyName = Regex.Replace(cleanCompanyName, enPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 5: Remove leftover symbols like . , ; : at the end or between words (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, "[.,;:]+", "");

            // Step 6: Handle common abbreviations (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\b(Grp|Gp)\b", "Group", RegexOptions.IgnoreCase);
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"®|™|©", "", RegexOptions.IgnoreCase);

            // Step 7: Clean up whitespace and special characters
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\s+", " ").Trim();

            // Step 8: Tokenize and singularize (with blacklist)
            cleanCompanyName = ApplyTokenizeAndSingularize(cleanCompanyName, detectedLanguage);

            // Final trim to remove any leading/trailing spaces before returning
            cleanCompanyName = cleanCompanyName.Trim();

            return string.IsNullOrWhiteSpace(cleanCompanyName) ? companyName : cleanCompanyName;
        }

        /// <summary>
        /// Applies tokenization and singularization to the input string based on the detected language.
        /// </summary>
        /// <param name="input"></param>
        /// <param name="detectedLanguage"></param>
        /// <returns></returns>
        private static string ApplyTokenizeAndSingularize(string input, string detectedLanguage)
        {
            // Blacklist: languages for which this step should NOT run
            var blacklist = new HashSet<string> { "hi" }; // Add more language codes as needed
            if (blacklist.Contains(detectedLanguage))
                return input;

            // Tokenize using Unicode letters
            var words = Regex.Matches(input, @"\p{L}+").OfType<Match>().Select(m => m.Value).ToArray();
            // Singularize: if more than 1 word and word length > 6, trim trailing 's'
            var result = string.Join(" ", words.Select(a => words.Length > 1 && a.Length > 6 ? a.TrimEnd('s') : a));
            return result;
        }

    }
}